
import java.text.SimpleDateFormat

pipeline {
    agent any
    tools { nodejs "n14" }
    
    // 移除environment，改用parameters配置
    
    parameters {
        string(name: 'GIT_REPO_URL', defaultValue: 'http://**************:19523/consumer-web-frontend/consumer-petsafe-admin.git', description: '项目Git仓库地址')
        string(name: 'GIT_BRANCH', defaultValue: 'preview', description: 'Git分支名称')
        string(name: 'GIT_CREDENTIALS_ID', defaultValue: 'cicd', description: 'Git凭据ID')
        string(name: 'PROJECT_DIR', defaultValue: 'consumer-petsafe-admin', description: '项目目录名称')
        string(name: 'BUILD_OUTPUT_DIR', defaultValue: 'dist', description: '构建输出目录名称')
        string(name: 'SSH_CONFIG_NAME', defaultValue: '**************', description: 'SSH服务器配置名称')
        string(name: 'DEPLOY_BASE_PATH', defaultValue: '/home/<USER>/openresty/nginx', description: '部署基础路径')
        string(name: 'DEPLOY_DIR_NAME', defaultValue: 'html_coolpet_internal', description: '部署目录名称')
        choice(name: 'isInstall', choices: ['no', 'yes'], description: '是否重新安装依赖')
        choice(name: 'deployStrategy', choices: ['test', 'production'], description: '部署策略：test=覆盖部署，production=增量同步')
    }

    stages {
        stage('Checkout Project') {
            steps {
                checkout([$class: 'GitSCM',
                    branches: [[name: "*/${params.GIT_BRANCH}"]],
                    extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: "${params.PROJECT_DIR}"]],
                    userRemoteConfigs: [[
                        credentialsId: "${params.GIT_CREDENTIALS_ID}",
                        url: "${params.GIT_REPO_URL}"
                    ]]
                ])
            }
        }

        stage('Build Application') {
            steps {
                sh '''
                    echo "开始构建前端项目..."
                    pwd
                    node -v
                    npm -v

                    cd ${PROJECT_DIR}

                    if [ "$isInstall" = "yes" ]; then
                        echo "清理依赖并重新安装..."
                        rm -rf node_modules package-lock.json
                        npm install
                    else
                        echo "跳过依赖安装"
                    fi

                    echo "执行构建..."
                    npm run build

                    echo "构建完成"
                '''
            }
        }

        stage('Package Build Artifacts') {
            steps {
                sh '''
                    cd ${PROJECT_DIR}

                    echo "打包构建产物..."
                    rm -rf app.tar.gz
                    tar -zcvf app.tar.gz -C ${BUILD_OUTPUT_DIR} .

                    echo "打包完成: app.tar.gz"
                    ls -la app.tar.gz
                '''
            }
        }



        stage('Deploy to Remote Server') {
            steps {
                sshPublisher(publishers: [
                    sshPublisherDesc(
                        configName: "${params.SSH_CONFIG_NAME}",
                        transfers: [sshTransfer(
                            cleanRemote: false,
                            excludes: '',
                            execCommand: '''
                            echo "开始部署到服务器..."
                            mkdir -p ${DEPLOY_BASE_PATH}/${DEPLOY_DIR_NAME}

                            # 备份当前版本
                            if [ -d "${DEPLOY_BASE_PATH}/${DEPLOY_DIR_NAME}" ] && [ "$(ls -A ${DEPLOY_BASE_PATH}/${DEPLOY_DIR_NAME})" ]; then
                                echo "备份当前版本..."
                                cd ${DEPLOY_BASE_PATH}
                                tar -zcf ${DEPLOY_DIR_NAME}-backup-$(date +%Y%m%d%H%M).tar.gz -C ${DEPLOY_DIR_NAME} . 2>/dev/null || echo "备份跳过"
                            fi

                            # 移动上传的包到部署目录
                            mv ${DEPLOY_BASE_PATH}/app.tar.gz ${DEPLOY_BASE_PATH}/${DEPLOY_DIR_NAME}/
                            cd ${DEPLOY_BASE_PATH}/${DEPLOY_DIR_NAME}

                            echo "[部署新版本 - 策略: ${deployStrategy}]"

                            if [ "${deployStrategy}" = "test" ]; then
                                echo "使用测试环境策略：完全覆盖部署"
                                # 清空部署目录
                                rm -rf *
                                # 解压新版本
                                tar -zxf app.tar.gz
                            else
                                echo "使用生产环境策略：只替换同名文件"
                                # 创建临时目录
                                TEMP_DIR="temp_$(date +%Y%m%d%H%M)"
                                mkdir -p $TEMP_DIR

                                # 解压到临时目录
                                tar -zxf app.tar.gz -C $TEMP_DIR

                                # 复制文件，保持目录结构
                                cp -r $TEMP_DIR/* ./

                                # 清理临时目录
                                rm -rf $TEMP_DIR
                                echo "文件替换完成"
                            fi

                            # 清理包文件
                            rm -f app.tar.gz

                            echo "部署完成到: ${DEPLOY_BASE_PATH}/${DEPLOY_DIR_NAME}"
                            echo "当前目录文件数量: $(find . -type f | wc -l)"
                            ls -la | head -10
                            ''',
                            execTimeout: 600000,
                            flatten: false,
                            makeEmptyDirs: false,
                            noDefaultExcludes: false,
                            patternSeparator: '[, ]+',
                            remoteDirectory: "${params.DEPLOY_BASE_PATH}",
                            remoteDirectorySDF: false,
                            removePrefix: "${params.PROJECT_DIR}/",
                            sourceFiles: "${params.PROJECT_DIR}/app.tar.gz"
                        )],
                        usePromotionTimestamp: false,
                        useWorkspaceInPromotion: false,
                        verbose: true
                    )
                ])
            }
        }


    }

    post {
        success {
            echo '前端项目构建和部署成功！'
            echo "部署服务器: ${params.SSH_CONFIG_NAME}"
            echo "部署路径: ${params.DEPLOY_BASE_PATH}/${params.DEPLOY_DIR_NAME}"
        }
        failure {
            echo '前端项目构建或部署失败，请检查日志！'
        }
        always {
            echo "Jenkins 工作空间清理完成"
        }
    }
}